/* packageB/my/order/index.wxss */
.nav{
	position: fixed;
	left: 0;
	top: 0;
	width: 100%;
	border-top: 1rpx solid #F7F7F7;
	background: #fff;
	display: flex;
	flex-wrap: wrap;
  justify-content: space-around;
  background: #fff;
  z-index: 9;
}
.nav view{
	font-size: 30rpx;
	line-height: 98rpx;
	color: #333;
	position: relative;
	padding: 0 14rpx;
}
.nav view.active{
	color: #2DA7F6;
	font-weight: bold;
}
.nav view.active::after{
	content: '';
	width: 100%;
	height: 3rpx;
	background: #2DA7F6;
	position: absolute;
	left: 0;
	bottom: 0;
}

.list{
	padding: 120rpx 30rpx 20rpx;
}
.list .nr{
	background: #fff;
	border-radius: 10rpx;
	margin-bottom: 20rpx;
}
.list .nr .xx .title{
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	border-bottom: 1rpx solid #EDF1F7;
	padding: 0 25rpx;
}
.list .nr .xx .title .numb{
	font-size: 28rpx;
	line-height: 80rpx;
	color: #999;
}
.list .nr .xx .title .state{
	font-size: 28rpx;
	line-height: 80rpx;
	font-weight: bold;
	color: #2DA7F6;
}
.list .nr .xx .text{
	padding: 20rpx 25rpx;
}
.list .nr .xx .text view{
	font-size: 24rpx;
	line-height: 30rpx;
	color: #999;
}
.list .nr .xx .text .name{
	margin-bottom: 16rpx;
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
}
.list .nr .xx .text .name view{
	font-size: 34rpx;
	line-height: 48rpx;
	color: #333;
	font-weight: bold;
}
.list .nr .xx .text .name text{
	font-size: 38rpx;
	line-height: 48rpx;
	color: #333;
	font-weight: bold;
}
.list .nr .xx .text .address{
	margin-bottom: 14rpx;
	display: flex;
	flex-wrap: wrap;
}
.list .nr .xx .text .address image{
	width: auto;
	height: 30rpx;
}
.list .nr .xx .text .address view{
	font-size: 24rpx;
	line-height: 30rpx;
	color: #999;
	margin-left: 10rpx;
}
.list .nr .xx .text .stime{
	position: relative;
	padding-left: 32rpx;
}
.list .nr .xx .text .stime::before{
	content: '';
	position: absolute;
	width: 14rpx;
	height: 14rpx;
	background: #11CC3D;
	border-radius: 50%;
	left: 5rpx;
	top: 50%;
	transform: translateY(-50%);
}
.list .nr .xx .text .etime{
	position: relative;
	margin-top: 14rpx;
	padding-left: 32rpx;
}
.list .nr .xx .text .etime::before{
	content: '';
	position: absolute;
	width: 14rpx;
	height: 14rpx;
	background: #2298F9;
	border-radius: 50%;
	left: 5rpx;
	top: 50%;
	transform: translateY(-50%);
}
.list .nr .xx .text .pay{
	margin-top: 14rpx;
	display: flex;
	flex-wrap: wrap;
	align-items: center;
}
.list .nr .xx .text .pay image{
	width: 22rpx;
	height: 23rpx;
}
.list .nr .xx .text .pay view{
	font-size: 24rpx;
	line-height: 30rpx;
	color: #999;
	margin-left: 10rpx;
}

/* 抵扣信息样式 */
.list .nr .xx .text .deduction-info{
	margin-top: 14rpx;
	padding: 16rpx;
	background: #F8F9FA;
	border-radius: 8rpx;
	border-left: 4rpx solid #2DA7F6;
}
.list .nr .xx .text .deduction-info .deduction-item{
	font-size: 24rpx;
	line-height: 32rpx;
	color: #666;
	margin-bottom: 8rpx;
}
.list .nr .xx .text .deduction-info .deduction-item:last-child{
	margin-bottom: 0;
}
.list .nr .xx .text .deduction-info .remaining-amount{
	font-size: 26rpx;
	line-height: 36rpx;
	color: #FF6B35;
	font-weight: bold;
	margin-top: 8rpx;
}
.list .nr .btn{
	display: flex;
	flex-wrap: wrap;
	justify-content: flex-end;
	padding: 20rpx 25rpx;
	border-top: 1rpx solid #EDF1F7;
}
.list .nr .btn view{
	font-size: 28rpx;
	line-height: 60rpx;
	color: #2298F9;
	border: 1rpx solid #2298F9;
	border-radius: 30rpx;
	padding: 0 24rpx;
}


