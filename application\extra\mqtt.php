<?php
return [
    'server' => '39.106.91.24',
    'port' => 1883,
    'username' => 'disuo',
    'password' => 'disuo2025',
    'client_id' => 'php_server_subscriber_dz',
//    'client_id' => 'mqttx_0941c6a1',
    'watch_file_path'=>ROOT_PATH.'/application/common/library/workerman_events',//监控文件路径

    // 将 watch_file_path 从字符串修改为数组，可以监控多个目录
//    'watch_file_path'=> [
//        ROOT_PATH.'/application/common/library/workerman_events',
//        ROOT_PATH.'/application/common/logic'
//    ],
    'watch_file_change'=>true,//是否启用文件监控
    'reconnect_interval'=>10,
];

