<!--packageB/my/order/index.wxml-->
<view class="container">
	<view class="nav">
		<view class="{{item.state == type?'active':''}}" wx:for="{{states}}" bindtap="getState" data-type="{{item.state}}">{{item.title}}</view>
	</view>
	<view class="list">
		<view class="nr" wx:for="{{list}}">
			<view class="xx">
				<view class="title">
					<view class="numb">订单编号：{{item.sn}}</view>
					<view class="state" wx:if="{{item.status == 1}}">使用中</view>
					<view class="state" wx:if="{{item.status == 2}}">待支付</view>
					<view class="state" wx:if="{{item.status == 3}}">已完成</view>
				</view>
				<view class="text">
					<view class="name">
						<view>{{item.equipment_name}}</view>
						<text>¥ {{item.money}}</text>
					</view>
					<view class="address">
						<image src="/image/icon_address.png" mode="heightFix"></image>
						<view>地址：{{item.hospital_addr}}</view>
					</view>
					<view class="stime">开始时间：{{item.createtime}}</view>
					<view class="etime" wx:if="{{item.status != 1}}">结束时间：{{item.returntime}}</view>

					<!-- 显示抵扣信息 -->
					<view class="deduction-info" wx:if="{{item.status == 2 && (item.deposit_deducted > 0 || item.balance_deducted > 0)}}">
						<view wx:if="{{item.deposit_deducted > 0}}" class="deduction-item">押金抵扣：¥{{item.deposit_deducted}}</view>
						<view wx:if="{{item.balance_deducted > 0}}" class="deduction-item">余额抵扣：¥{{item.balance_deducted}}</view>
						<view class="remaining-amount">待支付：¥{{item.remaining_amount}}</view>
					</view>

					<view class="pay" wx:if="{{item.status == 3}}">
						<image src="/image/icon_zffs.png"></image>
						<view>支付方式：{{item.pay_types_str}}</view>
					</view>
				</view>
			</view>
			<view class="btn" wx:if="{{item.status == 2}}">
				<view wx:if="{{item.pay_status == 1}}" catch:tap="goPay" data-money="{{item.remaining_amount}}" data-ordercode="{{item.sn}}">立即支付</view>
				<view wx:if="{{item.pay_status == 3}}" catch:tap="getPay" data-pay_status="{{item.pay_status}}" data-ordercode="{{item.sn}}">我要免单</view>
				<view wx:if="{{item.pay_status == 5}}" data-pay_status="{{item.pay_status}}" data-ordercode="{{item.sn}}" catch:tap="getPay">套餐抵扣</view>
			</view>
		</view>

		<view class='loading' wx:if="{{list.length > 0}}">{{loadTitle}}</view>

		<view class="zwsj" wx:else>
			<image src="/image/icon_wsj.png" mode="widthFix"></image>
			<view>暂无订单</view>
		</view>

	</view>
</view>