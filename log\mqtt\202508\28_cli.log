[ 2025-08-28T17:11:26+08:00 ][ error ] 收到消息：dz/pi/getstatus/110002
error
[ 2025-08-28T17:11:26+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/110002',
  'message' => '{"VER":"777","CMD":"12","CD":"110002","SIMID":"898604E0092220769724","CS":"0","DT":"1221","CH1":"1221","CH2":"1700","LS":"0","SS":"0","BS":"11.6","RSSI":27,"MT":"2","NG":"0"}',
  'timestamp' => '2025-08-28 17:11:26',
)
error
[ 2025-08-28T17:11:26+08:00 ][ error ] 解析数据：
error
[ 2025-08-28T17:11:26+08:00 ][ error ] array (
  'VER' => '777',
  'CMD' => '12',
  'CD' => '110002',
  'SIMID' => '898604E0092220769724',
  'CS' => '0',
  'DT' => '1221',
  'CH1' => '1221',
  'CH2' => '1700',
  'LS' => '0',
  'SS' => '0',
  'BS' => '11.6',
  'RSSI' => 27,
  'MT' => '2',
  'NG' => '0',
)
error
[ 2025-08-28T17:11:26+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/110002
error
[ 2025-08-28T17:11:26+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-28T17:11:26+08:00 ][ log ] 【状态上报】开始处理地锁 '110002' 的通用状态...
log
[ 2025-08-28T17:11:26+08:00 ][ log ] 【状态上报】成功更新地锁 '110002' 的快照状态到数据库
log
[ 2025-08-28T17:11:26+08:00 ][ log ] 【状态上报】成功记录地锁 '110002' 的状态到日志表
log
[ 2025-08-28T17:11:26+08:00 ][ log ] 状态明细
log
[ 2025-08-28T17:11:26+08:00 ][ log ] array (
  'sn' => '110002',
  'message_type' => '2',
  'lock_status' => 0,
  'car_status' => 0,
  'voltage' => 11.6,
  'signal_strength' => 27,
  'error_code' => 0,
  'raw_payload' => '{"VER":"777","CMD":"12","CD":"110002","SIMID":"898604E0092220769724","CS":"0","DT":"1221","CH1":"1221","CH2":"1700","LS":"0","SS":"0","BS":"11.6","RSSI":27,"MT":"2","NG":"0"}',
  'createtime' => 1756372286,
)
log
[ 2025-08-28T17:11:26+08:00 ][ log ] 【业务触发】检查 '110002' 的状态变化...
log
[ 2025-08-28T17:11:26+08:00 ][ log ] 打印设备信息
log
[ 2025-08-28T17:11:26+08:00 ][ log ] array (
  'id' => 40,
  'sn' => '110002',
  'mainname' => '110002 测试四轮地锁',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 1,
  'voltage' => '11.60',
  'signal_strength' => 27,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 1,
  'last_online_time' => 1756342942,
  'last_status_updatetime' => 1756372215,
  'createtime' => 1756194042,
  'updatetime' => 1756372215,
  'hardware_type' => 2,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => '110002',
  'qrcode' => 'uploads/ewm/batch/final_qr_110002.png',
  'kehu_id' => 2,
  'lat' => '28.8517',
  'lng' => '112.9254',
  'address' => '湖南省岳阳市汨罗市岳阳市求实饲料有限公司东北(尚磊路东)',
)
log
[ 2025-08-28T17:11:26+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-28T17:11:26+08:00 ][ log ] 0
log
[ 2025-08-28T17:11:26+08:00 ][ log ] 【业务触发】检测到地锁 '110002' 车辆离开，尝试自动结束订单。
log
[ 2025-08-28T17:11:26+08:00 ][ log ] 打印四轮地锁order信息
log
[ 2025-08-28T17:11:26+08:00 ][ log ] array (
  'id' => 215,
  'platform_id' => 20,
  'agent_id' => 82,
  'hospital_id' => 48,
  'hospital_fcbl' => 20.0,
  'hospital_price' => '0.01',
  'hospital_hourlong' => 1,
  'hospital_freedt' => 0,
  'departments_id' => 34,
  'equipment_id' => 40,
  'equipment_info_id' => 0,
  'info' => '总平台-深圳代理商-南山物业-物业北车棚-110002 测试四轮地锁-110002',
  'sn' => 'ord2025082816353529510900',
  'user_id' => 5611,
  'money' => '0.00',
  'status' => '1',
  'pay_types' => '0',
  'pay_status' => '0',
  'pay_time' => NULL,
  'createtime' => 1756370135,
  'returntime' => NULL,
  'updatetime' => 1756370135,
  'is_branch' => '1',
  'admin_info' => NULL,
  'timelong' => 0,
  'timelong_fenzhong' => 0,
  'really_money' => '0.00',
  'actreturntime' => NULL,
  'answer_return_time' => 1756393200,
  'overtime' => NULL,
  'overtime_money' => NULL,
  'normal_money' => NULL,
  'charging_rule' => 1,
  'use_status' => 2,
  'nb_goodsid' => '',
  'deposit_deducted' => '0.00',
  'balance_deducted' => '0.00',
  'remaining_amount' => '0.00',
  'payment_detail' => NULL,
  'setmeal_deducted' => 0,
  'setmeal_log_id' => NULL,
)
log
[ 2025-08-28T17:11:26+08:00 ][ log ] === 进入orderEnd1方法 ===
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 输入参数 $order_info:
log
[ 2025-08-28T17:11:26+08:00 ][ log ] array (
  'id' => 215,
  'platform_id' => 20,
  'agent_id' => 82,
  'hospital_id' => 48,
  'hospital_fcbl' => 20.0,
  'hospital_price' => '0.01',
  'hospital_hourlong' => 1,
  'hospital_freedt' => 0,
  'departments_id' => 34,
  'equipment_id' => 40,
  'equipment_info_id' => 0,
  'info' => '总平台-深圳代理商-南山物业-物业北车棚-110002 测试四轮地锁-110002',
  'sn' => 'ord2025082816353529510900',
  'user_id' => 5611,
  'money' => '0.00',
  'status' => '1',
  'pay_types' => '0',
  'pay_status' => '0',
  'pay_time' => NULL,
  'createtime' => 1756370135,
  'returntime' => NULL,
  'updatetime' => 1756370135,
  'is_branch' => '1',
  'admin_info' => NULL,
  'timelong' => 0,
  'timelong_fenzhong' => 0,
  'really_money' => '0.00',
  'actreturntime' => NULL,
  'answer_return_time' => 1756393200,
  'overtime' => NULL,
  'overtime_money' => NULL,
  'normal_money' => NULL,
  'charging_rule' => 1,
  'use_status' => 2,
  'nb_goodsid' => '',
  'deposit_deducted' => '0.00',
  'balance_deducted' => '0.00',
  'remaining_amount' => '0.00',
  'payment_detail' => NULL,
  'setmeal_deducted' => 0,
  'setmeal_log_id' => NULL,
)
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 初始化返回数据 $return:
log
[ 2025-08-28T17:11:26+08:00 ][ log ] array (
  'success' => true,
  'msg' => '',
)
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 当前时间戳 $time: 1756372286
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 当前时间格式化: 2025-08-28 17:11:26
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 检查订单状态 $order_info[status]: 1
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 订单状态为1，开始处理结束订单逻辑
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 订单创建时间 $createtime: 1756370135
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 订单创建时间格式化: 2025-08-28 16:35:35
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 时间差（秒）$time_diff_seconds: 2151
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 使用时长（小时）$timelong: 1
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 使用时长（分钟）$timelong_fenzhong: 36
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 初始化费用变量 - $overtime: 0, $overtime_money: 0, $normal_money: 0
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 查询医院信息，hospital_id: 48
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 医院信息 $hospital:
log
[ 2025-08-28T17:11:26+08:00 ][ log ] array (
  'id' => 48,
  'platform_id' => 20,
  'agent_id' => 82,
  'code' => '',
  'name' => '南山物业',
  'addr' => '南山区政府',
  'join_type' => '1',
  'fcbl' => 20.0,
  'price' => '0.01',
  'hourlong' => 1,
  'freedt' => 0,
  'notes' => NULL,
  'corpname' => NULL,
  'kefu' => '12345678',
  'logo_image' => '/uploads/20250811/af755f587cc2ab6562de3efaed08dd87.png',
  'introduce_content' => NULL,
  'status' => '1',
  'createtime' => 1665987657,
  'updatetime' => 1756369903,
  'route' => '[{"id":82,"fcbl":"50"}]',
  'balance' => '0.00',
  'user_id' => 0,
  'use_start' => '00',
  'use_end' => '23',
  'charging_rule' => 1,
  'overtime_price' => '0.00',
  'contract_price' => '5.00',
  'longitude' => '113.93052',
  'latitude' => '22.53315',
  'yajin' => '0.03',
)
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 计费规则 $hospital[charging_rule]: 1
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 按小时计费模式
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 时间限制设置 use_start: 00, use_end: 23
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 开启了时间限制
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 预约归还时间 answer_return_time: 1756393200
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 预约归还时间格式化: 2025-08-28 23:00:00
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 没有超时归还
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 计费参数 - timelong: 1, hospital_hourlong: 1, hospital_price: 0.01
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 计算费用（无超时）$money: 0.01
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 费用计算完成，开始初始化订单更新数据
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 初始化订单更新数据 $order_update_data:
log
[ 2025-08-28T17:11:26+08:00 ][ log ] array (
  'status' => 2,
  'returntime' => 1756372286,
  'timelong' => 1.0,
  'timelong_fenzhong' => 36.0,
  'money' => 0.01,
  'really_money' => 0.01,
  'overtime' => 0,
  'overtime_money' => 0,
  'normal_money' => 0,
  'charging_rule' => 1,
  'deposit_deducted' => 0.0,
  'balance_deducted' => 0.0,
  'remaining_amount' => 0.01,
  'setmeal_log_id' => 0,
  'payment_detail' => '',
)
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 开始判断是否需要支付
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 使用时长（分钟）: 36, 免费时间（分钟）: 0
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 使用时长大于免费时间，需要支付
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 设置支付状态为1（需要支付）
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 检查是否需要进行抵扣逻辑，pay_status: 1
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 需要支付，开始抵扣逻辑处理
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 第一步：检查用户套餐
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 套餐查询条件 $log_where:
log
[ 2025-08-28T17:11:26+08:00 ][ log ] array (
  'user_id' => 5611,
  'effective_start' => 
  array (
    0 => '<=',
    1 => 1756372286,
  ),
  'effective_end' => 
  array (
    0 => '>=',
    1 => 1756372286,
  ),
  'deletetime' => NULL,
  'status' => 2,
)
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 当前时间: 2025-08-28 17:11:26
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 套餐查询结果 $setmeal_log:
log
[ 2025-08-28T17:11:26+08:00 ][ log ] NULL
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 没有找到有效套餐，进行押金和余额抵扣
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 调用processPaymentDeduction方法
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 抵扣参数详细信息:
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - user_id: 5611
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - money: 0.01
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - money类型: double
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - money原始值: 0.01
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - order_id: 215
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - money参数正常，为正数: 0.01
log
[ 2025-08-28T17:11:26+08:00 ][ log ] === 进入processPaymentDeduction方法 ===
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - 输入参数:
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - user_id: 5611
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - total_amount: 0.01
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - total_amount类型: double
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - total_amount原始值: 0.01
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - order_id: 215
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - total_amount参数正常，为非负数: 0.01
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - 初始化结果数组
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - 初始化结果数组 $result:
log
[ 2025-08-28T17:11:26+08:00 ][ log ] array (
  'success' => true,
  'msg' => '',
  'deposit_deducted' => 0.0,
  'balance_deducted' => 0.0,
  'remaining_amount' => 0.01,
  'payment_detail' => '',
  'refund_amount' => 0.0,
)
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - 开始获取用户信息，user_id: 5611
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - 数据库查询用户信息完成
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - 查询到的用户信息 $user:
log
[ 2025-08-28T17:11:26+08:00 ][ log ] array (
  'id' => 5611,
  'group_id' => 1,
  'username' => 'hskh_iZmJwk',
  'nickname' => '11',
  'password' => 'ff2ab1b000e0176999f32e350d5c3ce4',
  'salt' => 'u1IEqg',
  'email' => '',
  'mobile' => '17616716907',
  'avatar' => 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0',
  'level' => 0,
  'gender' => 0,
  'birthday' => NULL,
  'bio' => '',
  'money' => '0.00',
  'score' => 0,
  'successions' => 2,
  'maxsuccessions' => 2,
  'prevtime' => 1755740064,
  'logintime' => 1755846855,
  'loginip' => '************',
  'loginfailure' => 0,
  'joinip' => '************',
  'jointime' => 1754534277,
  'createtime' => 1754534277,
  'updatetime' => 1756366523,
  'token' => '',
  'status' => 'normal',
  'verification' => '',
  'types' => '1',
  'openid' => 'oh6FKvsDIYL4O5ff2qHuDhJYAd0U',
  'deposit' => '0.03',
  'deposit_id' => 30,
  'is_maintain' => '1',
  'session_key' => 'dOvQQ+3kFNYJuyAClcfMXg==',
  'is_subscribe' => 1,
  'subscribetime' => NULL,
)
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - 开始提取和转换用户数据
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - 用户数据提取完成:
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - current_deposit (押金余额): 0.03
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - current_balance (账户余额): 0
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - remaining_amount (剩余需支付): 0.01
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - payment_steps (支付步骤): []
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - 数据合理性检查:
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - current_deposit >= 0: true
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - current_balance >= 0: true
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - remaining_amount > 0: true
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - === 第一步：押金抵扣逻辑 ===
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - 检查押金抵扣条件:
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - current_deposit > 0: true
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - remaining_amount > 0: true
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - 条件判断结果: 满足押金抵扣条件
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - 进入押金抵扣逻辑
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - 比较押金与剩余金额:
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - current_deposit: 0.03
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - remaining_amount: 0.01
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - current_deposit >= remaining_amount: true (押金充足)
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - === 押金充足，完全抵扣分支 ===
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - 押金充足抵扣计算结果:
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - deposit_used (使用押金): 0.01
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - deposit_refund (退款押金): 0.02
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - remaining_amount (剩余金额): 0
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - 更新结果数组:
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - result[deposit_deducted]: 0.01
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - result[refund_amount]: 0.02
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - 添加支付步骤记录:
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - payment_steps: [{"step":1,"type":"deposit_deduction","amount":0.01,"remaining_order":0}]
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - 检查是否需要押金退款:
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - deposit_refund > 0: true (需要退款)
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - === 处理押金退款 ===
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - 添加退款步骤记录:
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - payment_steps: [{"step":1,"type":"deposit_deduction","amount":0.01,"remaining_order":0},{"step":2,"type":"deposit_refund","amount":0.019999999999999997,"status":"pending"}]
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - 调用processDepositRefund方法处理退款
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - 退款参数 - user: {"id":5611,"group_id":1,"username":"hskh_iZmJwk","nickname":"11","password":"ff2ab1b000e0176999f32e350d5c3ce4","salt":"u1IEqg","email":"","mobile":"17616716907","avatar":"https:\/\/mmbiz.qpic.cn\/mmbiz\/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg\/0","level":0,"gender":0,"birthday":null,"bio":"","money":"0.00","score":0,"successions":2,"maxsuccessions":2,"prevtime":1755740064,"logintime":1755846855,"loginip":"************","loginfailure":0,"joinip":"************","jointime":1754534277,"createtime":1754534277,"updatetime":1756366523,"token":"","status":"normal","verification":"","types":"1","openid":"oh6FKvsDIYL4O5ff2qHuDhJYAd0U","deposit":"0.03","deposit_id":30,"is_maintain":"1","session_key":"dOvQQ+3kFNYJuyAClcfMXg==","is_subscribe":1,"subscribetime":null}
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - 退款参数 - deposit_refund: 0.02
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - 退款参数 - order_id: 215
log
[ 2025-08-28T17:11:26+08:00 ][ log ] 打印退款回调地址
log
[ 2025-08-28T17:11:26+08:00 ][ log ] https://xcx.gjgxds.com/api/pay/refundnotify
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - 退款处理结果 $refund_result:
log
[ 2025-08-28T17:11:26+08:00 ][ log ] array (
  'success' => false,
  'msg' => '微信退款失败：退款处理异常：Undefined index: trade_state',
)
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - 押金退款失败，返回错误
log
[ 2025-08-28T17:11:26+08:00 ][ log ] processPaymentDeduction - 错误结果 $result:
log
[ 2025-08-28T17:11:26+08:00 ][ log ] array (
  'success' => false,
  'msg' => '押金退款处理失败：微信退款失败：退款处理异常：Undefined index: trade_state',
  'deposit_deducted' => 0.01,
  'balance_deducted' => 0.0,
  'remaining_amount' => 0.01,
  'payment_detail' => '',
  'refund_amount' => 0.019999999999999997,
)
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 抵扣结果 $deduction_result:
log
[ 2025-08-28T17:11:26+08:00 ][ log ] array (
  'success' => false,
  'msg' => '押金退款处理失败：微信退款失败：退款处理异常：Undefined index: trade_state',
  'deposit_deducted' => 0.01,
  'balance_deducted' => 0.0,
  'remaining_amount' => 0.01,
  'payment_detail' => '',
  'refund_amount' => 0.019999999999999997,
)
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 抵扣失败: 押金退款处理失败：微信退款失败：退款处理异常：Undefined index: trade_state
log
[ 2025-08-28T17:11:26+08:00 ][ log ] orderEnd1 - 返回错误结果 $return:
log
[ 2025-08-28T17:11:26+08:00 ][ log ] array (
  'success' => false,
  'msg' => '押金退款处理失败：微信退款失败：退款处理异常：Undefined index: trade_state',
)
log
[ 2025-08-28T17:11:26+08:00 ][ log ] 打印orderEnd1结果
log
[ 2025-08-28T17:11:26+08:00 ][ log ] array (
  'success' => false,
  'msg' => '押金退款处理失败：微信退款失败：退款处理异常：Undefined index: trade_state',
)
log
[ 2025-08-28T17:11:26+08:00 ][ log ] 【业务触发】订单(SN:ord2025082816353529510900)自动结束和结算失败: 押金退款处理失败：微信退款失败：退款处理异常：Undefined index: trade_state
log
