[ 2025-08-28T14:43:41+08:00 ][ error ] 收到消息：dz/pi/getstatus/110002
error
[ 2025-08-28T14:43:41+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/110002',
  'message' => '{"VER":"666","CMD":"12","CD":"110002","SIMID":"898604E0092220769724","CS":"0","DT":"1221","CH1":"1221","CH2":"1700","LS":"0","SS":"0","BS":"11.6","RSSI":27,"MT":"2","NG":"0"}',
  'timestamp' => '2025-08-28 14:43:41',
)
error
[ 2025-08-28T14:43:41+08:00 ][ error ] 解析数据：
error
[ 2025-08-28T14:43:41+08:00 ][ error ] array (
  'VER' => '666',
  'CMD' => '12',
  'CD' => '110002',
  'SIMID' => '898604E0092220769724',
  'CS' => '0',
  'DT' => '1221',
  'CH1' => '1221',
  'CH2' => '1700',
  'LS' => '0',
  'SS' => '0',
  'BS' => '11.6',
  'RSSI' => 27,
  'MT' => '2',
  'NG' => '0',
)
error
[ 2025-08-28T14:43:41+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/110002
error
[ 2025-08-28T14:43:41+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-28T14:43:41+08:00 ][ log ] 【状态上报】开始处理地锁 '110002' 的通用状态...
log
[ 2025-08-28T14:43:41+08:00 ][ log ] 【状态上报】成功更新地锁 '110002' 的快照状态到数据库
log
[ 2025-08-28T14:43:41+08:00 ][ log ] 【状态上报】成功记录地锁 '110002' 的状态到日志表
log
[ 2025-08-28T14:43:41+08:00 ][ log ] 状态明细
log
[ 2025-08-28T14:43:41+08:00 ][ log ] array (
  'sn' => '110002',
  'message_type' => '2',
  'lock_status' => 0,
  'car_status' => 0,
  'voltage' => 11.6,
  'signal_strength' => 27,
  'error_code' => 0,
  'raw_payload' => '{"VER":"666","CMD":"12","CD":"110002","SIMID":"898604E0092220769724","CS":"0","DT":"1221","CH1":"1221","CH2":"1700","LS":"0","SS":"0","BS":"11.6","RSSI":27,"MT":"2","NG":"0"}',
  'createtime' => 1756363421,
)
log
[ 2025-08-28T14:43:41+08:00 ][ log ] 【业务触发】检查 '110002' 的状态变化...
log
[ 2025-08-28T14:43:41+08:00 ][ log ] 打印设备信息
log
[ 2025-08-28T14:43:41+08:00 ][ log ] array (
  'id' => 40,
  'sn' => '110002',
  'mainname' => '110002 测试四轮地锁',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 1,
  'voltage' => '11.60',
  'signal_strength' => 27,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 1,
  'last_online_time' => 1756342942,
  'last_status_updatetime' => 1756363348,
  'createtime' => 1756194042,
  'updatetime' => 1756363348,
  'hardware_type' => 2,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => '110002',
  'qrcode' => 'uploads/ewm/batch/final_qr_110002.png',
  'kehu_id' => 2,
  'lat' => '28.8517',
  'lng' => '112.9254',
  'address' => '湖南省岳阳市汨罗市岳阳市求实饲料有限公司东北(尚磊路东)',
)
log
[ 2025-08-28T14:43:41+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-28T14:43:41+08:00 ][ log ] 0
log
[ 2025-08-28T14:43:41+08:00 ][ log ] 【业务触发】检测到地锁 '110002' 车辆离开，尝试自动结束订单。
log
[ 2025-08-28T14:43:41+08:00 ][ log ] 打印四轮地锁order信息
log
[ 2025-08-28T14:43:41+08:00 ][ log ] array (
  'id' => 214,
  'platform_id' => 20,
  'agent_id' => 82,
  'hospital_id' => 48,
  'hospital_fcbl' => 20.0,
  'hospital_price' => '0.20',
  'hospital_hourlong' => 1,
  'hospital_freedt' => 0,
  'departments_id' => 34,
  'equipment_id' => 40,
  'equipment_info_id' => 0,
  'info' => '总平台-深圳代理商-南山物业-物业北车棚-110002 测试四轮地锁-110002',
  'sn' => 'ord2025082814351696765988',
  'user_id' => 5611,
  'money' => '0.00',
  'status' => '1',
  'pay_types' => '0',
  'pay_status' => '0',
  'pay_time' => NULL,
  'createtime' => 1756362916,
  'returntime' => NULL,
  'updatetime' => 1756362916,
  'is_branch' => '1',
  'admin_info' => NULL,
  'timelong' => 0,
  'timelong_fenzhong' => 0,
  'really_money' => '0.00',
  'actreturntime' => NULL,
  'answer_return_time' => 1756310400,
  'overtime' => NULL,
  'overtime_money' => NULL,
  'normal_money' => NULL,
  'charging_rule' => 1,
  'use_status' => 2,
  'nb_goodsid' => '',
  'deposit_deducted' => '0.00',
  'balance_deducted' => '0.00',
  'remaining_amount' => '0.00',
  'payment_detail' => NULL,
  'setmeal_deducted' => 0,
  'setmeal_log_id' => NULL,
)
log
[ 2025-08-28T14:43:41+08:00 ][ log ] === 进入orderEnd1方法 ===
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 输入参数 $order_info:
log
[ 2025-08-28T14:43:41+08:00 ][ log ] array (
  'id' => 214,
  'platform_id' => 20,
  'agent_id' => 82,
  'hospital_id' => 48,
  'hospital_fcbl' => 20.0,
  'hospital_price' => '0.20',
  'hospital_hourlong' => 1,
  'hospital_freedt' => 0,
  'departments_id' => 34,
  'equipment_id' => 40,
  'equipment_info_id' => 0,
  'info' => '总平台-深圳代理商-南山物业-物业北车棚-110002 测试四轮地锁-110002',
  'sn' => 'ord2025082814351696765988',
  'user_id' => 5611,
  'money' => '0.00',
  'status' => '1',
  'pay_types' => '0',
  'pay_status' => '0',
  'pay_time' => NULL,
  'createtime' => 1756362916,
  'returntime' => NULL,
  'updatetime' => 1756362916,
  'is_branch' => '1',
  'admin_info' => NULL,
  'timelong' => 0,
  'timelong_fenzhong' => 0,
  'really_money' => '0.00',
  'actreturntime' => NULL,
  'answer_return_time' => 1756310400,
  'overtime' => NULL,
  'overtime_money' => NULL,
  'normal_money' => NULL,
  'charging_rule' => 1,
  'use_status' => 2,
  'nb_goodsid' => '',
  'deposit_deducted' => '0.00',
  'balance_deducted' => '0.00',
  'remaining_amount' => '0.00',
  'payment_detail' => NULL,
  'setmeal_deducted' => 0,
  'setmeal_log_id' => NULL,
)
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 初始化返回数据 $return:
log
[ 2025-08-28T14:43:41+08:00 ][ log ] array (
  'success' => true,
  'msg' => '',
)
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 当前时间戳 $time: 1756363421
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 当前时间格式化: 2025-08-28 14:43:41
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 检查订单状态 $order_info[status]: 1
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 订单状态为1，开始处理结束订单逻辑
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 订单创建时间 $createtime: 1756362916
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 订单创建时间格式化: 2025-08-28 14:35:16
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 时间差（秒）$time_diff_seconds: 505
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 使用时长（小时）$timelong: 1
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 使用时长（分钟）$timelong_fenzhong: 9
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 初始化费用变量 - $overtime: 0, $overtime_money: 0, $normal_money: 0
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 查询医院信息，hospital_id: 48
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 医院信息 $hospital:
log
[ 2025-08-28T14:43:41+08:00 ][ log ] array (
  'id' => 48,
  'platform_id' => 20,
  'agent_id' => 82,
  'code' => '',
  'name' => '南山物业',
  'addr' => '南山区政府',
  'join_type' => '1',
  'fcbl' => 20.0,
  'price' => '0.20',
  'hourlong' => 1,
  'freedt' => 0,
  'notes' => NULL,
  'corpname' => NULL,
  'kefu' => '12345678',
  'logo_image' => '/uploads/20250811/af755f587cc2ab6562de3efaed08dd87.png',
  'introduce_content' => NULL,
  'status' => '1',
  'createtime' => 1665987657,
  'updatetime' => 1756342487,
  'route' => '[{"id":82,"fcbl":"50"}]',
  'balance' => '0.00',
  'user_id' => 0,
  'use_start' => '00',
  'use_end' => '00',
  'charging_rule' => 1,
  'overtime_price' => '0.00',
  'contract_price' => '5.00',
  'longitude' => '113.93052',
  'latitude' => '22.53315',
  'yajin' => '0.03',
)
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 计费规则 $hospital[charging_rule]: 1
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 按小时计费模式
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 时间限制设置 use_start: 00, use_end: 00
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 开启了时间限制
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 预约归还时间 answer_return_time: 1756310400
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 预约归还时间格式化: 2025-08-28 00:00:00
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 超时归还
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 正常时间段小时数 $normal_time_hours: -14
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 正常时间段费用 $normal_money: -2.8
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 超时时长（小时）$overtime: 15
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 超时单价 overtime_price: 0.00
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 超时费用 $overtime_money: 0
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 总费用（正常+超时）$money: -2.8
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 费用计算完成，开始初始化订单更新数据
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 初始化订单更新数据 $order_update_data:
log
[ 2025-08-28T14:43:41+08:00 ][ log ] array (
  'status' => 2,
  'returntime' => 1756363421,
  'timelong' => 1.0,
  'timelong_fenzhong' => 9.0,
  'money' => -2.8000000000000003,
  'really_money' => -2.8000000000000003,
  'overtime' => 15.0,
  'overtime_money' => 0.0,
  'normal_money' => -2.8000000000000003,
  'charging_rule' => 1,
  'deposit_deducted' => 0.0,
  'balance_deducted' => 0.0,
  'remaining_amount' => -2.8000000000000003,
  'setmeal_log_id' => 0,
  'payment_detail' => '',
)
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 开始判断是否需要支付
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 使用时长（分钟）: 9, 免费时间（分钟）: 0
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 使用时长大于免费时间，需要支付
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 设置支付状态为1（需要支付）
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 检查是否需要进行抵扣逻辑，pay_status: 1
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 需要支付，开始抵扣逻辑处理
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 第一步：检查用户套餐
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 套餐查询条件 $log_where:
log
[ 2025-08-28T14:43:41+08:00 ][ log ] array (
  'user_id' => 5611,
  'effective_start' => 
  array (
    0 => '<=',
    1 => 1756363421,
  ),
  'effective_end' => 
  array (
    0 => '>=',
    1 => 1756363421,
  ),
  'deletetime' => NULL,
  'status' => 2,
)
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 当前时间: 2025-08-28 14:43:41
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 套餐查询结果 $setmeal_log:
log
[ 2025-08-28T14:43:41+08:00 ][ log ] NULL
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 没有找到有效套餐，进行押金和余额抵扣
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 调用processPaymentDeduction方法
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 抵扣参数 - user_id: 5611, money: -2.8, order_id: 214
log
[ 2025-08-28T14:43:41+08:00 ][ log ] 进入processPaymentDeduction
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 抵扣结果 $deduction_result:
log
[ 2025-08-28T14:43:41+08:00 ][ log ] array (
  'success' => true,
  'msg' => '',
  'deposit_deducted' => 0.0,
  'balance_deducted' => 0.0,
  'remaining_amount' => -2.8000000000000003,
  'payment_detail' => '{"order_total":-2.8000000000000003,"payment_process":[],"final_status":"completed"}',
  'refund_amount' => 0.0,
)
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 抵扣成功，更新订单抵扣信息
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 抵扣信息更新:
log
[ 2025-08-28T14:43:41+08:00 ][ log ] array (
  'deposit_deducted' => 0.0,
  'balance_deducted' => 0.0,
  'remaining_amount' => -2.8000000000000003,
  'payment_detail' => '{"order_total":-2.8000000000000003,"payment_process":[],"final_status":"completed"}',
)
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 检查剩余金额，remaining_amount: -2.8
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 已完全抵扣，设置订单状态为完成
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 完全抵扣后状态:
log
[ 2025-08-28T14:43:41+08:00 ][ log ] array (
  'pay_status' => 1,
  'status' => 3,
)
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 设置实际归还时间 actreturntime: 1756363421
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 最终订单更新数据 $order_update_data:
log
[ 2025-08-28T14:43:41+08:00 ][ log ] array (
  'status' => 3,
  'returntime' => 1756363421,
  'timelong' => 1.0,
  'timelong_fenzhong' => 9.0,
  'money' => -2.8000000000000003,
  'really_money' => -2.8000000000000003,
  'overtime' => 15.0,
  'overtime_money' => 0.0,
  'normal_money' => -2.8000000000000003,
  'charging_rule' => 1,
  'deposit_deducted' => 0.0,
  'balance_deducted' => 0.0,
  'remaining_amount' => -2.8000000000000003,
  'setmeal_log_id' => 0,
  'payment_detail' => '{"order_total":-2.8000000000000003,"payment_process":[],"final_status":"completed"}',
  'pay_status' => 1,
  'actreturntime' => 1756363421,
)
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 开始更新订单信息，order_id: 214
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 订单更新结果 $res: 1
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 查询设备硬件类型，equipment_id: 40
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 设备硬件类型 $hardware_type: 2
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 硬件类型不为1，跳过设备状态更新
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 重新查询更新后的订单数据
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 更新后的订单信息 $order_info:
log
[ 2025-08-28T14:43:41+08:00 ][ log ] array (
  'id' => 214,
  'platform_id' => 20,
  'agent_id' => 82,
  'hospital_id' => 48,
  'hospital_fcbl' => 20.0,
  'hospital_price' => '0.20',
  'hospital_hourlong' => 1,
  'hospital_freedt' => 0,
  'departments_id' => 34,
  'equipment_id' => 40,
  'equipment_info_id' => 0,
  'info' => '总平台-深圳代理商-南山物业-物业北车棚-110002 测试四轮地锁-110002',
  'sn' => 'ord2025082814351696765988',
  'user_id' => 5611,
  'money' => '-2.80',
  'status' => '3',
  'pay_types' => '0',
  'pay_status' => '1',
  'pay_time' => NULL,
  'createtime' => 1756362916,
  'returntime' => 1756363421,
  'updatetime' => 1756362916,
  'is_branch' => '1',
  'admin_info' => NULL,
  'timelong' => 1,
  'timelong_fenzhong' => 9,
  'really_money' => '-2.80',
  'actreturntime' => 1756363421,
  'answer_return_time' => 1756310400,
  'overtime' => 15,
  'overtime_money' => '0.00',
  'normal_money' => '-2.80',
  'charging_rule' => 1,
  'use_status' => 2,
  'nb_goodsid' => '',
  'deposit_deducted' => '0.00',
  'balance_deducted' => '0.00',
  'remaining_amount' => '-2.80',
  'payment_detail' => '{"order_total":-2.8000000000000003,"payment_process":[],"final_status":"completed"}',
  'setmeal_deducted' => 0,
  'setmeal_log_id' => 0,
)
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 设置返回数据 $return[data]
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 检查是否需要生成支付订单
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 检查条件 - pay_status: 1, remaining_amount: -2.80, return_success: true
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 不满足生成支付订单条件，跳过
log
[ 2025-08-28T14:43:41+08:00 ][ log ] orderEnd1 - 方法执行完成，最终返回结果 $return:
log
[ 2025-08-28T14:43:41+08:00 ][ log ] array (
  'success' => true,
  'msg' => '',
  'data' => 
  array (
    'id' => 214,
    'platform_id' => 20,
    'agent_id' => 82,
    'hospital_id' => 48,
    'hospital_fcbl' => 20.0,
    'hospital_price' => '0.20',
    'hospital_hourlong' => 1,
    'hospital_freedt' => 0,
    'departments_id' => 34,
    'equipment_id' => 40,
    'equipment_info_id' => 0,
    'info' => '总平台-深圳代理商-南山物业-物业北车棚-110002 测试四轮地锁-110002',
    'sn' => 'ord2025082814351696765988',
    'user_id' => 5611,
    'money' => '-2.80',
    'status' => '3',
    'pay_types' => '0',
    'pay_status' => '1',
    'pay_time' => NULL,
    'createtime' => 1756362916,
    'returntime' => 1756363421,
    'updatetime' => 1756362916,
    'is_branch' => '1',
    'admin_info' => NULL,
    'timelong' => 1,
    'timelong_fenzhong' => 9,
    'really_money' => '-2.80',
    'actreturntime' => 1756363421,
    'answer_return_time' => 1756310400,
    'overtime' => 15,
    'overtime_money' => '0.00',
    'normal_money' => '-2.80',
    'charging_rule' => 1,
    'use_status' => 2,
    'nb_goodsid' => '',
    'deposit_deducted' => '0.00',
    'balance_deducted' => '0.00',
    'remaining_amount' => '-2.80',
    'payment_detail' => '{"order_total":-2.8000000000000003,"payment_process":[],"final_status":"completed"}',
    'setmeal_deducted' => 0,
    'setmeal_log_id' => 0,
  ),
)
log
[ 2025-08-28T14:43:41+08:00 ][ log ] === orderEnd1方法结束 ===
log
[ 2025-08-28T14:43:41+08:00 ][ log ] 打印orderEnd1结果
log
[ 2025-08-28T14:43:41+08:00 ][ log ] array (
  'success' => true,
  'msg' => '',
  'data' => 
  array (
    'id' => 214,
    'platform_id' => 20,
    'agent_id' => 82,
    'hospital_id' => 48,
    'hospital_fcbl' => 20.0,
    'hospital_price' => '0.20',
    'hospital_hourlong' => 1,
    'hospital_freedt' => 0,
    'departments_id' => 34,
    'equipment_id' => 40,
    'equipment_info_id' => 0,
    'info' => '总平台-深圳代理商-南山物业-物业北车棚-110002 测试四轮地锁-110002',
    'sn' => 'ord2025082814351696765988',
    'user_id' => 5611,
    'money' => '-2.80',
    'status' => '3',
    'pay_types' => '0',
    'pay_status' => '1',
    'pay_time' => NULL,
    'createtime' => 1756362916,
    'returntime' => 1756363421,
    'updatetime' => 1756362916,
    'is_branch' => '1',
    'admin_info' => NULL,
    'timelong' => 1,
    'timelong_fenzhong' => 9,
    'really_money' => '-2.80',
    'actreturntime' => 1756363421,
    'answer_return_time' => 1756310400,
    'overtime' => 15,
    'overtime_money' => '0.00',
    'normal_money' => '-2.80',
    'charging_rule' => 1,
    'use_status' => 2,
    'nb_goodsid' => '',
    'deposit_deducted' => '0.00',
    'balance_deducted' => '0.00',
    'remaining_amount' => '-2.80',
    'payment_detail' => '{"order_total":-2.8000000000000003,"payment_process":[],"final_status":"completed"}',
    'setmeal_deducted' => 0,
    'setmeal_log_id' => 0,
  ),
)
log
[ 2025-08-28T14:43:41+08:00 ][ log ] 【业务触发】订单(SN:ord2025082814351696765988)自动结束和结算成功: 
log
